"use client"

import { Calendar, CreditCard, DollarSign, Bell, CalendarDays, Plus, Search, Filter, Clock, Users, MapPin } from "lucide-react"
import { useState } from "react"

export default function BookingPage() {
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0])

  // Sample booking data with more dates
  const bookings = [
    {
      id: 1,
      customerName: "Ahmed Ali",
      eventType: "Wedding",
      date: "2024-01-15",
      time: "6:00 PM - 11:00 PM",
      hall: "Grand Hall A",
      guests: 300,
      status: "confirmed",
      amount: 150000
    },
    {
      id: 2,
      customerName: "Fatima Khan",
      eventType: "Engagement",
      date: "2024-01-16",
      time: "4:00 PM - 8:00 PM",
      hall: "Royal Hall B",
      guests: 150,
      status: "pending",
      amount: 80000
    },
    {
      id: 3,
      customerName: "Hassan Sheikh",
      eventType: "Reception",
      date: "2024-01-17",
      time: "7:00 PM - 12:00 AM",
      hall: "Premium Hall C",
      guests: 250,
      status: "confirmed",
      amount: 120000
    },
    {
      id: 4,
      customerName: "<PERSON><PERSON> Ahmed",
      eventType: "Wedding",
      date: "2024-01-20",
      time: "5:00 PM - 10:00 PM",
      hall: "Grand Hall A",
      guests: 400,
      status: "confirmed",
      amount: 200000
    },
    {
      id: 5,
      customerName: "Omar Khan",
      eventType: "Birthday Party",
      date: "2024-01-22",
      time: "3:00 PM - 7:00 PM",
      hall: "Royal Hall B",
      guests: 100,
      status: "pending",
      amount: 50000
    }
  ]

  // Function to get bookings for selected date
  const getBookingsForDate = (date: string) => {
    return bookings.filter(booking => booking.date === date)
  }

  // Get bookings for currently selected date
  const selectedDateBookings = getBookingsForDate(selectedDate)

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'bg-green-100 text-green-800'
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'cancelled': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="flex h-screen">
      {/* Sidebar */}
      <aside className="w-64 bg-white border-r border-gray-200 p-5">
        <h2 className="text-xl font-bold mb-6 text-gray-800">Marriage Hall</h2>
        <nav className="space-y-2">
          <a href="/" className="block px-3 py-2 rounded-md hover:bg-gray-100 text-gray-700">
            Dashboard
          </a>
          <a href="/booking" className="block px-3 py-2 rounded-md bg-blue-100 text-blue-700">
            Booking Management
          </a>
          <a href="/dashboard" className="block px-3 py-2 rounded-md hover:bg-gray-100 text-gray-700">
            Dashboard Overview
          </a>
          <a href="/customers" className="block px-3 py-2 rounded-md hover:bg-gray-100 text-gray-700">
            Customers
          </a>
          <a href="/halls" className="block px-3 py-2 rounded-md hover:bg-gray-100 text-gray-700">
            Halls
          </a>
          <a href="/settings" className="block px-3 py-2 rounded-md hover:bg-gray-100 text-gray-700">
            Settings
          </a>
        </nav>
      </aside>

      {/* Main Content */}
      <div className="flex-1 overflow-hidden bg-gray-50">
        {/* Header */}
        <header className="flex h-16 items-end justify-between border-2 ml-3 mr-2 rounded-[12px] bg-white px-4 pb-4">
          <div className="flex items-center gap-3">
            <h1 className="text-2xl text-black font-semibold">Booking Management</h1>
            <CalendarDays className="h-6 w-6 text-blue-600" />
          </div>
          <button className="rounded-full bg-gray-100 p-2 hover:bg-gray-200 transition-colors">
            <Bell className="h-5 w-5 text-gray-600" />
          </button>
        </header>

        {/* Main Content */}
        <main className="p-4 h-full overflow-auto">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 gap-4 md:grid-cols-3 mb-6">
            <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between">
                <h2 className="font-medium text-gray-900">Today's Bookings</h2>
                <Calendar className="h-5 w-5 text-gray-400" />
              </div>
              <p className="mt-2 text-2xl font-bold text-gray-900">7</p>
              <p className="text-sm text-gray-500">+1 from yesterday</p>
            </div>

            <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between">
                <h2 className="font-medium text-gray-900">Pending Confirmations</h2>
                <CreditCard className="h-5 w-5 text-gray-400" />
              </div>
              <p className="mt-2 text-2xl font-bold text-gray-900">3</p>
              <p className="text-sm text-gray-500">Awaiting customer response</p>
            </div>

            <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between">
                <h2 className="font-medium text-gray-900">Total Revenue</h2>
                <DollarSign className="h-5 w-5 text-gray-400" />
              </div>
              <p className="mt-2 text-2xl font-bold text-gray-900">₹5,48,000</p>
              <p className="text-sm text-gray-500">+12% this month</p>
            </div>
          </div>

          {/* Action Bar */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1 flex gap-2">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search bookings..."
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
                />
              </div>
              <button className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors bg-white">
                <Filter className="h-4 w-4 text-gray-600" />
              </button>
            </div>
            <div className="flex gap-2">
              <div className="relative">
                <input
                  type="date"
                  value={selectedDate}
                  onChange={(e) => setSelectedDate(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
                />
              </div>
              <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center gap-2 transition-colors shadow-sm">
                <Plus className="h-4 w-4" />
                New Booking
              </button>
            </div>
          </div>

          {/* Calendar Date Status */}
          <div className="mb-6">
            <div className="rounded-lg border border-gray-200 bg-white shadow-sm p-6">
              <div className="flex items-center gap-3 mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Selected Date Status</h3>
                <Calendar className="h-5 w-5 text-blue-600" />
              </div>

              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-sm font-medium text-gray-700">Selected Date:</span>
                  <span className="text-sm font-semibold text-gray-900">
                    {new Date(selectedDate).toLocaleDateString('en-US', {
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </span>
                </div>

                {selectedDateBookings.length > 0 ? (
                  <div>
                    <div className="flex items-center gap-2 mb-3">
                      <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                      <span className="text-sm font-medium text-red-700">
                        Date is Booked - {selectedDateBookings.length} booking(s)
                      </span>
                    </div>

                    <div className="space-y-2">
                      {selectedDateBookings.map((booking) => (
                        <div key={booking.id} className="bg-white rounded-md p-3 border border-gray-200">
                          <div className="flex justify-between items-start">
                            <div>
                              <p className="font-medium text-gray-900">{booking.customerName}</p>
                              <p className="text-sm text-gray-600">{booking.eventType}</p>
                              <p className="text-sm text-gray-600">{booking.hall}</p>
                            </div>
                            <div className="text-right">
                              <p className="text-sm font-medium text-gray-900">{booking.time}</p>
                              <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(booking.status)}`}>
                                {booking.status}
                              </span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span className="text-sm font-medium text-green-700">
                      Date is Available - No bookings found
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Bookings List */}
          <div className="rounded-lg border border-gray-200 bg-white shadow-sm">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Recent Bookings</h2>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {bookings.map((booking) => (
                  <div key={booking.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors bg-white">
                    <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="font-semibold text-lg text-gray-900">{booking.customerName}</h3>
                          <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(booking.status)}`}>
                            {booking.status}
                          </span>
                        </div>
                        <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 text-sm text-gray-600">
                          <div className="flex items-center gap-1">
                            <Calendar className="h-4 w-4" />
                            {booking.date}
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className="h-4 w-4" />
                            {booking.time}
                          </div>
                          <div className="flex items-center gap-1">
                            <MapPin className="h-4 w-4" />
                            {booking.hall}
                          </div>
                        </div>
                        <div className="flex items-center gap-4 mt-2 text-sm">
                          <div className="flex items-center gap-1 text-gray-600">
                            <Users className="h-4 w-4" />
                            {booking.guests} guests
                          </div>
                          <div className="font-medium text-green-600">
                            ₹{booking.amount.toLocaleString()}
                          </div>
                          <div className="text-gray-500">
                            {booking.eventType}
                          </div>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <button className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 text-sm text-gray-700 transition-colors">
                          View
                        </button>
                        <button className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 text-sm text-gray-700 transition-colors">
                          Edit
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}