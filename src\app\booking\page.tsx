"use client"

import { Calendar, CreditCard, DollarSign, Bell, CalendarDays, Plus, Search, Filter, Clock, Users, MapPin } from "lucide-react"
import { useState } from "react"

export default function BookingPage() {
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0])
  const [showBookingModal, setShowBookingModal] = useState(false)
  const [newBookingDate, setNewBookingDate] = useState('')
  const [newBookingTime, setNewBookingTime] = useState('')
  const [showFilterDropdown, setShowFilterDropdown] = useState(false)
  const [viewMode, setViewMode] = useState('selected-date') // 'selected-date' or 'all-bookings'
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedEventType, setSelectedEventType] = useState('all')

  // Event categories
  const eventCategories = [
    { value: 'all', label: 'All Events' },
    { value: 'Wedding', label: 'Wedding' },
    { value: 'Birthday', label: 'Birthday' },
    { value: 'Milad', label: 'Milad' },
    { value: 'Aqiqa', label: 'Aqiqa' },
    { value: 'Valima', label: 'Valima' },
    { value: 'Engagement', label: 'Engagement' },
    { value: 'Anniversary', label: 'Anniversary' },
    { value: 'Other', label: 'Other' }
  ]

  // Sample booking data with more dates
  const bookings = [
    {
      id: 1,
      customerName: "Ahmed Ali",
      eventType: "Wedding",
      date: "2024-01-15",
      time: "6:00 PM - 11:00 PM",
      hall: "Grand Hall A",
      guests: 300,
      status: "confirmed",
      amount: 150000
    },
    {
      id: 2,
      customerName: "Fatima Khan",
      eventType: "Engagement",
      date: "2024-01-16",
      time: "4:00 PM - 8:00 PM",
      hall: "Royal Hall B",
      guests: 150,
      status: "pending",
      amount: 80000
    },
    {
      id: 3,
      customerName: "Hassan Sheikh",
      eventType: "Reception",
      date: "2024-01-17",
      time: "7:00 PM - 12:00 AM",
      hall: "Premium Hall C",
      guests: 250,
      status: "confirmed",
      amount: 120000
    },
    {
      id: 4,
      customerName: "Zara Ahmed",
      eventType: "Wedding",
      date: "2024-01-20",
      time: "5:00 PM - 10:00 PM",
      hall: "Grand Hall A",
      guests: 400,
      status: "confirmed",
      amount: 200000
    },
    {
      id: 5,
      customerName: "Omar Khan",
      eventType: "Birthday Party",
      date: "2024-01-22",
      time: "3:00 PM - 7:00 PM",
      hall: "Royal Hall B",
      guests: 100,
      status: "pending",
      amount: 50000
    }
  ]

  // Function to get bookings for selected date
  const getBookingsForDate = (date: string) => {
    return bookings.filter(booking => booking.date === date)
  }

  // Get bookings for currently selected date
  const selectedDateBookings = getBookingsForDate(selectedDate)

  // Filter bookings based on search query and event type
  const filterBookings = (bookingsList: typeof bookings) => {
    let filtered = bookingsList

    // Filter by event type
    if (selectedEventType !== 'all') {
      filtered = filtered.filter(booking => booking.eventType === selectedEventType)
    }

    // Filter by search query
    if (searchQuery.trim()) {
      filtered = filtered.filter(booking =>
        booking.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        booking.eventType.toLowerCase().includes(searchQuery.toLowerCase()) ||
        booking.hall.toLowerCase().includes(searchQuery.toLowerCase()) ||
        booking.status.toLowerCase().includes(searchQuery.toLowerCase()) ||
        booking.date.toLowerCase().includes(searchQuery.toLowerCase()) ||
        booking.time.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    return filtered
  }

  // Get filtered bookings based on current view mode and search
  const getDisplayBookings = () => {
    if (viewMode === 'selected-date') {
      return filterBookings(selectedDateBookings)
    } else {
      return filterBookings(bookings)
    }
  }

  const displayBookings = getDisplayBookings()

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'bg-green-100 text-green-800'
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'cancelled': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="flex h-screen">
      {/* Sidebar */}
      <aside className="w-64 bg-white border-r border-gray-200 p-5">
        <h2 className="text-xl font-bold mb-6 text-gray-800">Marriage Hall</h2>
        <nav className="space-y-2">
          <a href="/" className="block px-3 py-2 rounded-md hover:bg-gray-100 text-gray-700">
            Dashboard
          </a>
          <a href="/booking" className="block px-3 py-2 rounded-md bg-blue-100 text-blue-700">
            Booking Management
          </a>
          <a href="/dashboard" className="block px-3 py-2 rounded-md hover:bg-gray-100 text-gray-700">
            Dashboard Overview
          </a>
          <a href="/customers" className="block px-3 py-2 rounded-md hover:bg-gray-100 text-gray-700">
            Customers
          </a>
          <a href="/halls" className="block px-3 py-2 rounded-md hover:bg-gray-100 text-gray-700">
            Halls
          </a>
          <a href="/settings" className="block px-3 py-2 rounded-md hover:bg-gray-100 text-gray-700">
            Settings
          </a>
        </nav>
      </aside>

      {/* Main Content */}
      <div className="flex-1 overflow-hidden bg-gray-50">
        {/* Header */}
        <header className="flex h-16 items-end justify-between border-2 ml-3 mr-2 rounded-[12px] bg-white px-4 pb-4">
          <div className="flex items-center gap-3">
            <h1 className="text-2xl text-black font-semibold">Booking Management</h1>
            <CalendarDays className="h-6 w-6 text-blue-600" />
          </div>
          <button className="rounded-full bg-gray-100 p-2 hover:bg-gray-200 transition-colors">
            <Bell className="h-5 w-5 text-gray-600" />
          </button>
        </header>

        {/* Main Content */}
        <main className="p-4 h-full overflow-auto">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 gap-4 md:grid-cols-3 mb-6">
            <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between">
                <h2 className="font-medium text-gray-900">Today's Bookings</h2>
                <Calendar className="h-5 w-5 text-gray-400" />
              </div>
              <p className="mt-2 text-2xl font-bold text-gray-900">7</p>
              <p className="text-sm text-gray-500">+1 from yesterday</p>
            </div>

            <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between">
                <h2 className="font-medium text-gray-900">Pending Confirmations</h2>
                <CreditCard className="h-5 w-5 text-gray-400" />
              </div>
              <p className="mt-2 text-2xl font-bold text-gray-900">3</p>
              <p className="text-sm text-gray-500">Awaiting customer response</p>
            </div>

            <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between">
                <h2 className="font-medium text-gray-900">Total Revenue</h2>
                <DollarSign className="h-5 w-5 text-gray-400" />
              </div>
              <p className="mt-2 text-2xl font-bold text-gray-900">₹5,48,000</p>
              <p className="text-sm text-gray-500">+12% this month</p>
            </div>
          </div>

          {/* Action Bar */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1 flex gap-2">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search bookings..."
                  className="w-full pl-10 pr-10 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-black placeholder-gray-500"
                />
                {searchQuery && (
                  <button
                    onClick={() => setSearchQuery('')}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                    title="Clear search"
                  >
                    <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                )}
              </div>
              <div className="relative">
                <button
                  onClick={() => setShowFilterDropdown(!showFilterDropdown)}
                  className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors bg-white flex items-center gap-2"
                >
                  <Filter className="h-4 w-4 text-gray-600" />
                  <span className="text-sm text-gray-700">Filter</span>
                </button>

                {/* Filter Dropdown */}
                {showFilterDropdown && (
                  <div className="absolute top-full left-0 mt-2 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
                    <div className="p-2">
                      <button
                        onClick={() => {
                          setViewMode('selected-date')
                          setShowFilterDropdown(false)
                        }}
                        className={`w-full text-left px-3 py-2 rounded-md hover:bg-gray-50 transition-colors ${
                          viewMode === 'selected-date' ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
                        }`}
                      >
                        Selected Date Bookings
                      </button>
                      <button
                        onClick={() => {
                          setViewMode('all-bookings')
                          setShowFilterDropdown(false)
                        }}
                        className={`w-full text-left px-3 py-2 rounded-md hover:bg-gray-50 transition-colors ${
                          viewMode === 'all-bookings' ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
                        }`}
                      >
                        All Recent Bookings
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
            <div className="flex gap-2">
              {/* Event Categories Dropdown */}
              <div className="relative">
                <select
                  value={selectedEventType}
                  onChange={(e) => setSelectedEventType(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-black appearance-none pr-8 min-w-[140px]"
                >
                  {eventCategories.map((category) => (
                    <option key={category.value} value={category.value} className="text-black">
                      {category.label}
                    </option>
                  ))}
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                  <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </div>

              <button
                onClick={() => setShowBookingModal(true)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center gap-2 transition-colors shadow-sm"
              >
                <Plus className="h-4 w-4" />
                New Booking
              </button>
            </div>
          </div>

          {/* Calendar Date Status / Search Results */}
          <div className="mb-6">
            <div className="rounded-lg border border-gray-200 bg-white shadow-sm p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <h3 className="text-lg font-semibold text-gray-900">
                    {searchQuery ? `Search Results for "${searchQuery}"` : 'Selected Date Status'}
                  </h3>
                  <Calendar className="h-5 w-5 text-blue-600" />
                </div>

                {/* Date Picker */}
                {!searchQuery && (
                  <div className="relative">
                    <input
                      type="date"
                      value={selectedDate}
                      onChange={(e) => setSelectedDate(e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-black"
                    />
                  </div>
                )}
              </div>

              {searchQuery ? (
                // Search Results Display
                <div className="space-y-3">
                  {displayBookings.length > 0 ? (
                    <div>
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-3">
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                          <span className="text-sm font-medium text-blue-700">
                            Found {displayBookings.length} booking(s)
                            {searchQuery && ` matching "${searchQuery}"`}
                            {selectedEventType !== 'all' && ` for ${selectedEventType} events`}
                          </span>
                        </div>
                      </div>

                      <div className="space-y-2">
                        {displayBookings.map((booking) => (
                          <div key={booking.id} className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <div className="flex justify-between items-start">
                              <div>
                                <p className="font-semibold text-gray-900">{booking.customerName}</p>
                                <p className="text-sm text-gray-600">{booking.eventType} at {booking.hall}</p>
                                <p className="text-sm font-medium text-blue-700">
                                  {booking.date} at {booking.time}
                                </p>
                                <p className="text-sm text-gray-600">{booking.guests} guests • ₹{booking.amount.toLocaleString()}</p>
                              </div>
                              <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(booking.status)}`}>
                                {booking.status}
                              </span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-6">
                      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                        <div className="text-gray-500 mb-2">No bookings found for "{searchQuery}"</div>
                        <button
                          onClick={() => setSearchQuery('')}
                          className="text-blue-600 hover:text-blue-800 text-sm underline"
                        >
                          Clear search to see date status
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                // Date Status Display
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-sm font-medium text-gray-700">Selected Date:</span>
                    <span className="text-sm font-semibold text-gray-900">
                      {new Date(selectedDate).toLocaleDateString('en-US', {
                        weekday: 'long',
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}
                    </span>
                  </div>

                  {selectedDateBookings.length > 0 ? (
                    <div>
                      <div className="flex items-center gap-2 mb-3">
                        <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                        <span className="text-sm font-medium text-red-700">
                          Date is Booked - {selectedDateBookings.length} booking(s)
                        </span>
                      </div>

                      <div className="space-y-2">
                        <p className="text-sm font-medium text-gray-700">Booked Time Slots:</p>
                        {selectedDateBookings.map((booking) => (
                          <div key={booking.id} className="bg-white rounded-md p-3 border border-gray-200">
                            <div className="flex justify-between items-start">
                              <div>
                                <p className="font-medium text-gray-900">{booking.customerName}</p>
                                <p className="text-sm text-gray-600">{booking.eventType} • {booking.hall}</p>
                              </div>
                              <div className="text-right">
                                <p className="text-sm font-medium text-blue-700">{booking.time}</p>
                                <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(booking.status)}`}>
                                  {booking.status}
                                </span>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <div>
                      <div className="flex items-center gap-2 mb-3">
                        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                        <span className="text-sm font-medium text-green-700">
                          Date is Available - No bookings found
                        </span>
                      </div>
                      <button
                        onClick={() => setShowBookingModal(true)}
                        className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm flex items-center gap-2"
                      >
                        <Plus className="h-4 w-4" />
                        Select Booking
                      </button>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Bookings List */}
          <div className="rounded-lg border border-gray-200 bg-white shadow-sm">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center gap-3">
                <h2 className="text-lg font-semibold text-gray-900">
                  {viewMode === 'selected-date'
                    ? `Bookings for ${new Date(selectedDate).toLocaleDateString('en-US', {
                        weekday: 'long',
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}`
                    : 'All Recent Bookings'
                  }
                </h2>
                <Calendar className="h-5 w-5 text-blue-600" />
              </div>
            </div>
            <div className="p-6">
              {viewMode === 'selected-date' ? (
                // Selected Date View
                displayBookings.length > 0 ? (
                  <div className="space-y-4">
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                        <span className="text-sm font-medium text-blue-700">
                          {searchQuery
                            ? `${displayBookings.length} booking(s) found matching "${searchQuery}"`
                            : `${displayBookings.length} booking(s) for ${new Date(selectedDate).toLocaleDateString('en-US', {
                                month: 'short',
                                day: 'numeric'
                              })}`
                          }
                        </span>
                      </div>
                      {searchQuery && (
                        <div className="mt-2">
                          <button
                            onClick={() => setSearchQuery('')}
                            className="text-xs text-blue-600 hover:text-blue-800 underline"
                          >
                            Clear search to see all bookings for this date
                          </button>
                        </div>
                      )}
                    </div>

                    {displayBookings.map((booking) => (
                    <div key={booking.id} className="border border-gray-200 rounded-lg p-5 hover:bg-gray-50 transition-colors bg-white shadow-sm">
                      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-3">
                            <h3 className="font-semibold text-xl text-gray-900">{booking.customerName}</h3>
                            <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(booking.status)}`}>
                              {booking.status}
                            </span>
                          </div>

                          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 mb-3">
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                              <Clock className="h-4 w-4 text-blue-500" />
                              <span className="font-medium">Time:</span>
                              <span>{booking.time}</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                              <MapPin className="h-4 w-4 text-green-500" />
                              <span className="font-medium">Hall:</span>
                              <span>{booking.hall}</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                              <Users className="h-4 w-4 text-purple-500" />
                              <span className="font-medium">Guests:</span>
                              <span>{booking.guests} people</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                              <DollarSign className="h-4 w-4 text-green-500" />
                              <span className="font-medium">Amount:</span>
                              <span className="font-semibold text-green-600">₹{booking.amount.toLocaleString()}</span>
                            </div>
                          </div>

                          <div className="bg-gray-50 rounded-lg p-3">
                            <div className="flex items-center gap-2 text-sm">
                              <span className="font-medium text-gray-700">Event Type:</span>
                              <span className="px-2 py-1 bg-white rounded-md text-gray-900 font-medium">{booking.eventType}</span>
                            </div>
                          </div>
                        </div>

                        <div className="flex flex-col gap-2 sm:flex-row">
                          <button className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 text-sm text-gray-700 transition-colors">
                            View Details
                          </button>
                          <button className="px-4 py-2 border border-blue-300 text-blue-700 rounded-lg hover:bg-blue-50 text-sm transition-colors">
                            Edit Booking
                          </button>
                          <button className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 text-sm transition-colors">
                            Cancel
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 max-w-md mx-auto">
                      <div className="flex items-center justify-center gap-2 mb-3">
                        <div className="w-4 h-4 bg-gray-500 rounded-full"></div>
                        <span className="text-lg font-medium text-gray-700">
                          {searchQuery ? 'No Search Results' : 'No Bookings Found'}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 mb-4">
                        {searchQuery
                          ? `No bookings found matching "${searchQuery}" for this date.`
                          : 'This date is completely available for new bookings.'
                        }
                      </p>
                      {searchQuery ? (
                        <button
                          onClick={() => setSearchQuery('')}
                          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2 mx-auto"
                        >
                          Clear Search
                        </button>
                      ) : (
                        <button
                          onClick={() => setShowBookingModal(true)}
                          className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2 mx-auto"
                        >
                          <Plus className="h-4 w-4" />
                          Book This Date
                        </button>
                      )}
                    </div>
                  </div>
                )
              ) : (
                // All Bookings View
                displayBookings.length > 0 ? (
                  <div className="space-y-4">
                    <div className="bg-purple-50 border border-purple-200 rounded-lg p-4 mb-4">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                        <span className="text-sm font-medium text-purple-700">
                          Showing {displayBookings.length} of {bookings.length} bookings
                          {searchQuery && ` matching "${searchQuery}"`}
                          {selectedEventType !== 'all' && ` for ${selectedEventType} events`}
                        </span>
                      </div>
                      {(searchQuery || selectedEventType !== 'all') && (
                        <div className="mt-2 flex gap-2">
                          {searchQuery && (
                            <button
                              onClick={() => setSearchQuery('')}
                              className="text-xs text-purple-600 hover:text-purple-800 underline"
                            >
                              Clear search
                            </button>
                          )}
                          {selectedEventType !== 'all' && (
                            <button
                              onClick={() => setSelectedEventType('all')}
                              className="text-xs text-purple-600 hover:text-purple-800 underline"
                            >
                              Clear event filter
                            </button>
                          )}
                          {(searchQuery || selectedEventType !== 'all') && (
                            <button
                              onClick={() => {
                                setSearchQuery('')
                                setSelectedEventType('all')
                              }}
                              className="text-xs text-purple-600 hover:text-purple-800 underline font-medium"
                            >
                              Clear all filters
                            </button>
                          )}
                        </div>
                      )}
                    </div>

                    {displayBookings.map((booking) => (
                    <div key={booking.id} className="border border-gray-200 rounded-lg p-5 hover:bg-gray-50 transition-colors bg-white shadow-sm">
                      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-3">
                            <h3 className="font-semibold text-xl text-gray-900">{booking.customerName}</h3>
                            <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(booking.status)}`}>
                              {booking.status}
                            </span>
                          </div>

                          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 mb-3">
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                              <Calendar className="h-4 w-4 text-blue-500" />
                              <span className="font-medium">Date:</span>
                              <span>{booking.date}</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                              <Clock className="h-4 w-4 text-blue-500" />
                              <span className="font-medium">Time:</span>
                              <span>{booking.time}</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                              <MapPin className="h-4 w-4 text-green-500" />
                              <span className="font-medium">Hall:</span>
                              <span>{booking.hall}</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                              <Users className="h-4 w-4 text-purple-500" />
                              <span className="font-medium">Guests:</span>
                              <span>{booking.guests} people</span>
                            </div>
                          </div>

                          <div className="bg-gray-50 rounded-lg p-3">
                            <div className="flex items-center gap-4 text-sm">
                              <div className="flex items-center gap-2">
                                <span className="font-medium text-gray-700">Event:</span>
                                <span className="px-2 py-1 bg-white rounded-md text-gray-900 font-medium">{booking.eventType}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <DollarSign className="h-4 w-4 text-green-500" />
                                <span className="font-medium text-gray-700">Amount:</span>
                                <span className="font-semibold text-green-600">₹{booking.amount.toLocaleString()}</span>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div className="flex flex-col gap-2 sm:flex-row">
                          <button className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 text-sm text-gray-700 transition-colors">
                            View Details
                          </button>
                          <button className="px-4 py-2 border border-blue-300 text-blue-700 rounded-lg hover:bg-blue-50 text-sm transition-colors">
                            Edit Booking
                          </button>
                          <button className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 text-sm transition-colors">
                            Cancel
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 max-w-md mx-auto">
                      <div className="flex items-center justify-center gap-2 mb-3">
                        <div className="w-4 h-4 bg-gray-500 rounded-full"></div>
                        <span className="text-lg font-medium text-gray-700">No Search Results</span>
                      </div>
                      <p className="text-sm text-gray-600 mb-4">
                        No bookings found matching "{searchQuery}".
                      </p>
                      <button
                        onClick={() => setSearchQuery('')}
                        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2 mx-auto"
                      >
                        Clear Search
                      </button>
                    </div>
                  </div>
                )
              )}
            </div>
          </div>
        </main>
      </div>

      {/* New Booking Modal */}
      {showBookingModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              {/* Modal Header */}
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-3">
                  <h2 className="text-2xl font-semibold text-gray-900">New Booking</h2>
                  <CalendarDays className="h-6 w-6 text-green-600" />
                </div>
                <button
                  onClick={() => setShowBookingModal(false)}
                  className="text-gray-800 hover:text-gray-600 transition-colors"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {/* Calendar Section */}
              <div className="mb-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Select Date & Time</h3>

                {/* Date Selection */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-800 mb-2">
                      Select Date
                    </label>
                    <input
                      type="date"
                      value={newBookingDate}
                      onChange={(e) => setNewBookingDate(e.target.value)}
                      min={new Date().toISOString().split('T')[0]}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />

                    {/* Date Status */}
                    {newBookingDate && (
                      <div className="mt-3 p-3 rounded-lg bg-gray-80">
                        <div className="flex items-center gap-2 mb-2">
                          <span className="text-sm font-medium text-gray-900">Selected:</span>
                          <span className="text-sm font-semibold text-gray-900">
                            {new Date(newBookingDate).toLocaleDateString('en-US', {
                              weekday: 'long',
                              year: 'numeric',
                              month: 'long',
                              day: 'numeric'
                            })}
                          </span>
                        </div>

                        {getBookingsForDate(newBookingDate).length > 0 ? (
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                            <span className="text-sm text-yellow-700">
                              {getBookingsForDate(newBookingDate).length} booking(s) already exist
                            </span>
                          </div>
                        ) : (
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                            <span className="text-sm text-green-700">Date is available</span>
                          </div>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Time Selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Select Time Slot
                    </label>
                    <select
                      value={newBookingTime}
                      onChange={(e) => setNewBookingTime(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-black"
                    >
                      <option value="" className="text-black">Choose time slot</option>
                      <option value="10:00 AM - 2:00 PM" className="text-black">10:00 AM - 2:00 PM (Morning)</option>
                      <option value="2:00 PM - 6:00 PM" className="text-black">2:00 PM - 6:00 PM (Afternoon)</option>
                      <option value="6:00 PM - 10:00 PM" className="text-black">6:00 PM - 10:00 PM (Evening)</option>
                      <option value="10:00 PM - 2:00 AM" className="text-black">10:00 PM - 2:00 AM (Night)</option>
                      <option value="Full Day" className="text-black">Full Day (10:00 AM - 2:00 AM)</option>
                    </select>

                    {newBookingTime && (
                      <div className="mt-3 p-3 rounded-lg bg-blue-50">
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4 text-blue-600" />
                          <span className="text-sm font-medium text-blue-700">
                            Selected: {newBookingTime}
                          </span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Existing Bookings for Selected Date */}
              {newBookingDate && getBookingsForDate(newBookingDate).length > 0 && (
                <div className="mb-6">
                  <h4 className="text-md font-medium text-gray-900 mb-3">Existing Bookings for This Date</h4>
                  <div className="space-y-2">
                    {getBookingsForDate(newBookingDate).map((booking) => (
                      <div key={booking.id} className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                        <div className="flex justify-between items-center">
                          <div>
                            <p className="font-medium text-gray-900">{booking.customerName}</p>
                            <p className="text-sm text-gray-600">{booking.eventType} - {booking.hall}</p>
                          </div>
                          <div className="text-right">
                            <p className="text-sm font-medium text-gray-900">{booking.time}</p>
                            <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(booking.status)}`}>
                              {booking.status}
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex gap-3 justify-end">
                <button
                  onClick={() => setShowBookingModal(false)}
                  className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors text-gray-700"
                >
                  Cancel
                </button>
                <button
                  onClick={() => {
                    if (newBookingDate && newBookingTime) {
                      alert(`Booking confirmed for ${newBookingDate} at ${newBookingTime}`)
                      setShowBookingModal(false)
                      setNewBookingDate('')
                      setNewBookingTime('')
                    } else {
                      alert('Please select both date and time')
                    }
                  }}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Confirm Booking
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}