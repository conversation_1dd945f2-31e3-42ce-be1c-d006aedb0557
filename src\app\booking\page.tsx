"use client"

import { Calendar, CreditCard, DollarSign, Bell } from "lucide-react"
// Update the import path below if your Card components are located elsewhere
import { Card, CardContent, CardHeader, CardTitle } from "../../components/ui/card"
// If the file does not exist, create 'card.tsx' in 'src/components/ui/' and export the components.
import { Button } from "../../components/ui/button"

export default function BookingPage() {
  return (
    <div className="flex-1 overflow-hidden">
      {/* Header */}
      <header className="flex h-16 items-end justify-between border-2 ml-3 mr-2 rounded-[12px] bg-white px-4 pb-4">
        <h1 className="text-2xl font-semibold">Booking</h1>
        <Button variant="ghost" size="icon" className="rounded-full bg-gray-100">
          <Bell className="h-5 w-5 text-gray-700" />
        </Button>
      </header>

      {/* Main Content */}
      <main className="p-4 h-full overflow-auto">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 gap-4 md:grid-cols-3 mb-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Today’s Bookings</CardTitle>
              <Calendar className="h-5 w-5 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">7</div>
              <p className="text-xs text-muted-foreground">+1 from yesterday</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending Confirmations</CardTitle>
              <CreditCard className="h-5 w-5 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">3</div>
              <p className="text-xs text-muted-foreground">Awaiting customer response</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Bookings</CardTitle>
              <DollarSign className="h-5 w-5 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">148</div>
              <p className="text-xs text-muted-foreground">+10 this month</p>
            </CardContent>
          </Card>
        </div>

        {/* Placeholder */}
        <Card className="text-center text-muted-foreground text-sm">
          <CardContent className="p-8">
            Booking form / calendar / details will appear here soon...
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
