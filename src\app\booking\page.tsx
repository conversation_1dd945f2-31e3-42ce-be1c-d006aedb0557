"use client"

import { Calendar, CreditCard, DollarSign, Bell, CalendarDays, Plus, Search, Clock, Users, MapPin, User } from "lucide-react"
import { useState } from "react"

export default function BookingPage() {
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0])
  const [showBookingModal, setShowBookingModal] = useState(false)
  const [newBookingDate, setNewBookingDate] = useState('')
  const [newBookingTime, setNewBookingTime] = useState('')

  // Registration form states
  const [customerName, setCustomerName] = useState('')
  const [customerPhone, setCustomerPhone] = useState('')
  const [customerEmail, setCustomerEmail] = useState('')
  const [eventType, setEventType] = useState('')
  const [guestCount, setGuestCount] = useState('')
  const [selectedHall, setSelectedHall] = useState('')
  const [specialRequests, setSpecialRequests] = useState('')
  const [showAllBookingsModal, setShowAllBookingsModal] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedEventType, setSelectedEventType] = useState('all')
  const [showCalendar, setShowCalendar] = useState(false)
  const [currentCalendarMonth, setCurrentCalendarMonth] = useState(0) // 0 = current month, 1 = next month, etc.

  // Event categories
  const eventCategories = [
    { value: 'all', label: 'All Events' },
    { value: 'Wedding', label: 'Wedding' },
    { value: 'Birthday', label: 'Birthday' },
    { value: 'Milad', label: 'Milad' },
    { value: 'Aqiqa', label: 'Aqiqa' },
    { value: 'Valima', label: 'Valima' },
    { value: 'Engagement', label: 'Engagement' },
    { value: 'Anniversary', label: 'Anniversary' },
    { value: 'Other', label: 'Other' }
  ]

  // Sample booking data with more dates
  const bookings = [
    {
      id: 1,
      customerName: "Ahmed Ali",
      eventType: "Wedding",
      date: "2024-01-15",
      time: "6:00 PM - 11:00 PM",
      hall: "Grand Hall A",
      guests: 300,
      status: "confirmed",
      amount: 150000
    },
    {
      id: 2,
      customerName: "Fatima Khan",
      eventType: "Engagement",
      date: "2024-01-16",
      time: "4:00 PM - 8:00 PM",
      hall: "Royal Hall B",
      guests: 150,
      status: "pending",
      amount: 80000
    },
    {
      id: 3,
      customerName: "Hassan Sheikh",
      eventType: "Reception",
      date: "2024-01-17",
      time: "7:00 PM - 12:00 AM",
      hall: "Premium Hall C",
      guests: 250,
      status: "confirmed",
      amount: 120000
    },
    {
      id: 4,
      customerName: "Zara Ahmed",
      eventType: "Wedding",
      date: "2024-01-20",
      time: "5:00 PM - 10:00 PM",
      hall: "Grand Hall A",
      guests: 400,
      status: "confirmed",
      amount: 200000
    },
    {
      id: 5,
      customerName: "Omar Khan",
      eventType: "Birthday Party",
      date: "2024-01-22",
      time: "3:00 PM - 7:00 PM",
      hall: "Royal Hall B",
      guests: 100,
      status: "pending",
      amount: 50000
    }
  ]

  // Function to get bookings for selected date
  const getBookingsForDate = (date: string) => {
    return bookings.filter(booking => booking.date === date)
  }

  // Get bookings for currently selected date
  const selectedDateBookings = getBookingsForDate(selectedDate)

  // Filter bookings based on search query and event type
  const filterBookings = (bookingsList: typeof bookings) => {
    let filtered = bookingsList

    // Filter by event type
    if (selectedEventType !== 'all') {
      filtered = filtered.filter(booking => booking.eventType === selectedEventType)
    }

    // Filter by search query
    if (searchQuery.trim()) {
      filtered = filtered.filter(booking =>
        booking.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        booking.eventType.toLowerCase().includes(searchQuery.toLowerCase()) ||
        booking.hall.toLowerCase().includes(searchQuery.toLowerCase()) ||
        booking.status.toLowerCase().includes(searchQuery.toLowerCase()) ||
        booking.date.toLowerCase().includes(searchQuery.toLowerCase()) ||
        booking.time.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    return filtered
  }

  // Get filtered bookings for the selected date
  const displayBookings = filterBookings(selectedDateBookings)

  // Auto-open calendar when searching for dates
  const handleSearchChange = (value: string) => {
    setSearchQuery(value)

    // Check if search contains date-related keywords
    const dateKeywords = ['date', 'day', 'today', 'tomorrow', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday', 'january', 'february', 'march', 'april', 'may', 'june', 'july', 'august', 'september', 'october', 'november', 'december']
    const searchLower = value.toLowerCase()

    if (dateKeywords.some(keyword => searchLower.includes(keyword)) && value.length > 2) {
      setShowCalendar(true)
    }
  }

  // Generate calendar dates for upcoming months (3 months ahead)
  const generateCalendarDates = (startMonth = 0) => {
    const today = new Date()
    const currentYear = today.getFullYear()
    const currentMonth = today.getMonth()

    const targetDate = new Date(currentYear, currentMonth + startMonth, 1)
    const year = targetDate.getFullYear()
    const month = targetDate.getMonth()

    // Get first day of month and how many days in month
    const firstDay = new Date(year, month, 1).getDay()
    const daysInMonth = new Date(year, month + 1, 0).getDate()

    // Generate calendar grid (42 days = 6 weeks)
    const dates = []

    // Previous month's trailing days
    for (let i = firstDay - 1; i >= 0; i--) {
      const prevDate = new Date(year, month, -i)
      dates.push({
        date: prevDate,
        dateStr: prevDate.toISOString().split('T')[0],
        isCurrentMonth: false,
        isPast: prevDate < today
      })
    }

    // Current month's days
    for (let day = 1; day <= daysInMonth; day++) {
      const currentDate = new Date(year, month, day)
      dates.push({
        date: currentDate,
        dateStr: currentDate.toISOString().split('T')[0],
        isCurrentMonth: true,
        isPast: currentDate < today
      })
    }

    // Next month's leading days
    const remainingSlots = 42 - dates.length
    for (let day = 1; day <= remainingSlots; day++) {
      const nextDate = new Date(year, month + 1, day)
      dates.push({
        date: nextDate,
        dateStr: nextDate.toISOString().split('T')[0],
        isCurrentMonth: false,
        isPast: false
      })
    }

    return { dates, month, year }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'bg-green-100 text-green-800'
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'cancelled': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="flex h-screen">
      {/* Sidebar */}
      <aside className="w-64 bg-white border-r border-gray-200 p-5">
        <h2 className="text-xl font-bold mb-6 text-gray-800">Marriage Hall</h2>
        <nav className="space-y-2">
          <a href="/" className="block px-3 py-2 rounded-md hover:bg-gray-100 text-gray-700">
            Dashboard
          </a>
          <a href="/booking" className="block px-3 py-2 rounded-md bg-blue-100 text-blue-700">
            Booking Management
          </a>
          <a href="/dashboard" className="block px-3 py-2 rounded-md hover:bg-gray-100 text-gray-700">
            Dashboard Overview
          </a>
          <a href="/customers" className="block px-3 py-2 rounded-md hover:bg-gray-100 text-gray-700">
            Customers
          </a>
          <a href="/halls" className="block px-3 py-2 rounded-md hover:bg-gray-100 text-gray-700">
            Halls
          </a>
          <a href="/settings" className="block px-3 py-2 rounded-md hover:bg-gray-100 text-gray-700">
            Settings
          </a>
        </nav>
      </aside>

      {/* Main Content */}
      <div className="flex-1 overflow-hidden bg-gray-50">
        {/* Header */}
        <header className="flex h-16 items-end justify-between border-2 ml-3 mr-2 rounded-[12px] bg-white px-4 pb-4">
          <div className="flex items-center gap-3">
            <h1 className="text-2xl text-black font-semibold">Booking Management</h1>
            <CalendarDays className="h-6 w-6 text-blue-600" />
          </div>
          <button className="rounded-full bg-gray-100 p-2 hover:bg-gray-200 transition-colors">
            <Bell className="h-5 w-5 text-gray-600" />
          </button>
        </header>

        {/* Main Content */}
        <main className="p-4 h-full overflow-auto">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 gap-4 md:grid-cols-3 mb-6">
            <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between">
                <h2 className="font-medium text-gray-900">Upcomming Booking</h2>
                <Calendar className="h-5 w-5 text-gray-400" />
              </div>
              <p className="mt-2 text-2xl font-bold text-gray-900">7</p>
              <p className="text-sm text-gray-500">+1 from yesterday</p>
            </div>

            <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between">
                <h2 className="font-medium text-gray-900">Pending Confirmations</h2>
                <CreditCard className="h-5 w-5 text-gray-400" />
              </div>
              <p className="mt-2 text-2xl font-bold text-gray-900">3</p>
              <p className="text-sm text-gray-500">Awaiting customer response</p>
            </div>

            <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between">
                <h2 className="font-medium text-gray-900">Total Revenue</h2>
                <DollarSign className="h-5 w-5 text-gray-400" />
              </div>
              <p className="mt-2 text-2xl font-bold text-gray-900">₹5,48,000</p>
              <p className="text-sm text-gray-500">+12% this month</p>
            </div>
          </div>

          {/* Action Bar */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1 flex gap-2">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => handleSearchChange(e.target.value)}
                  placeholder="Search bookings or type 'date', 'day', 'calendar' to open calendar..."
                  className="w-full pl-10 pr-10 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-black placeholder-gray-500"
                />
                {searchQuery && (
                  <button
                    onClick={() => setSearchQuery('')}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                    title="Clear search"
                  >
                    <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                )}
              </div>
              <button
                onClick={() => setShowAllBookingsModal(true)}
                className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors bg-white flex items-center gap-2"
              >
                <Calendar className="h-4 w-4 text-gray-600" />
                <span className="text-sm text-gray-700">All Recent Bookings</span>
              </button>
            </div>
            <div className="flex gap-2">
              {/* Event Categories Dropdown */}
              <div className="relative">
                <select
                  value={selectedEventType}
                  onChange={(e) => setSelectedEventType(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-black appearance-none pr-8 min-w-[140px]"
                >
                  {eventCategories.map((category) => (
                    <option key={category.value} value={category.value} className="text-black">
                      {category.label}
                    </option>
                  ))}
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                  <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </div>

              <button
                onClick={() => setShowBookingModal(true)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center gap-2 transition-colors shadow-sm"
              >
                <Plus className="h-4 w-4" />
                New Booking
              </button>
            </div>
          </div>

          {/* Date Status & Calendar Section */}
          <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Date Status & Calendar</h3>
              <CalendarDays className="h-5 w-5 text-blue-600" />
            </div>

            <div className="mb-4">
              <p className="text-sm text-gray-600 mb-2">Selected Date: {new Date(selectedDate).toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}</p>
              <div className="flex gap-2">
                <button
                  onClick={() => setShowCalendar(!showCalendar)}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
                >
                  <Calendar className="h-4 w-4" />
                  {showCalendar ? 'Hide Calendar' : 'Show Calendar'}
                </button>
              </div>
            </div>

            {/* Calendar Grid */}
            {showCalendar && (
              <div className="border-t pt-4">
                {/* Calendar Navigation */}
                <div className="flex items-center justify-between mb-4">
                  <button
                    onClick={() => setCurrentCalendarMonth(Math.max(-3, currentCalendarMonth - 1))}
                    disabled={currentCalendarMonth <= -3}
                    className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    ← Previous
                  </button>

                  <h4 className="text-lg font-medium text-gray-900">
                    {new Date(new Date().getFullYear(), new Date().getMonth() + currentCalendarMonth, 1).toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
                  </h4>

                  <button
                    onClick={() => setCurrentCalendarMonth(Math.min(12, currentCalendarMonth + 1))}
                    disabled={currentCalendarMonth >= 12}
                    className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    Next →
                  </button>
                </div>

                <div className="grid grid-cols-7 gap-2 mb-4">
                  {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
                    <div key={day} className="text-center text-sm font-medium text-gray-500 py-2">
                      {day}
                    </div>
                  ))}
                </div>

                <div className="grid grid-cols-7 gap-2">
                  {(() => {
                    const { dates } = generateCalendarDates(currentCalendarMonth)
                    return dates.map((dateInfo, i) => {
                      const hasBooking = bookings.some(booking => booking.date === dateInfo.dateStr)
                      const isSelected = selectedDate === dateInfo.dateStr
                      const isToday = new Date().toDateString() === dateInfo.date.toDateString()

                      return (
                        <button
                          key={i}
                          onClick={() => setSelectedDate(dateInfo.dateStr)}
                          disabled={dateInfo.isPast}
                          className={`
                            p-2 text-sm rounded-lg transition-colors relative
                            ${!dateInfo.isCurrentMonth ? 'text-gray-300' :
                              dateInfo.isPast ? 'text-gray-400 cursor-not-allowed' :
                              isSelected ? 'bg-blue-600 text-white' :
                              isToday ? 'bg-blue-100 text-blue-800' :
                              hasBooking ? 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200' :
                              'hover:bg-gray-100 text-gray-700'}
                          `}
                        >
                          {dateInfo.date.getDate()}
                          {hasBooking && dateInfo.isCurrentMonth && (
                            <div className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></div>
                          )}
                        </button>
                      )
                    })
                  })()}
                </div>

                <div className="mt-4 flex gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-blue-600 rounded"></div>
                    <span>Selected</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-yellow-100 border border-yellow-300 rounded"></div>
                    <span>Has Bookings</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-blue-100 border border-blue-300 rounded"></div>
                    <span>Today</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-gray-300 rounded"></div>
                    <span>Past/Other Month</span>
                  </div>
                </div>

                <div className="mt-3 text-xs text-gray-500 text-center">
                  Navigate 3 months back to 12 months ahead for advance bookings
                </div>
              </div>
            )}
          </div>

        </main>
      </div>

      {/* New Booking Modal */}
      {showBookingModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900">New Booking</h2>
                <button
                  onClick={() => setShowBookingModal(false)}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {/* Calendar for Date Selection */}
              <div className="mb-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Select Date & Time</h3>
                <div className="grid grid-cols-7 gap-2 mb-4">
                  {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
                    <div key={day} className="text-center text-sm font-medium text-gray-500 py-2">
                      {day}
                    </div>
                  ))}
                </div>
                <div className="grid grid-cols-7 gap-2 mb-4">
                  {Array.from({ length: 35 }, (_, i) => {
                    const date = new Date(2025, 6, i - 6) // July 2025
                    const dateStr = date.toISOString().split('T')[0]
                    const hasBooking = bookings.some(booking => booking.date === dateStr)
                    const isSelected = newBookingDate === dateStr

                    return (
                      <button
                        key={i}
                        onClick={() => setNewBookingDate(dateStr)}
                        className={`
                          p-2 text-sm rounded-lg transition-colors relative
                          ${isSelected ? 'bg-blue-600 text-white' :
                            hasBooking ? 'bg-red-100 text-red-800 cursor-not-allowed' :
                            'hover:bg-gray-100 text-gray-700'}
                        `}
                        disabled={hasBooking}
                      >
                        {date.getDate()}
                        {hasBooking && (
                          <div className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></div>
                        )}
                      </button>
                    )
                  })}
                </div>

                {/* Time Slots */}
                {newBookingDate && (
                  <div className="mt-4">
                    <h4 className="text-md font-medium text-gray-900 mb-3">Available Time Slots</h4>
                    <div className="grid grid-cols-2 gap-2">
                      {['Morning (9:00 AM - 1:00 PM)', 'Afternoon (2:00 PM - 6:00 PM)', 'Evening (6:00 PM - 11:00 PM)', 'Night (7:00 PM - 12:00 AM)'].map(timeSlot => (
                        <button
                          key={timeSlot}
                          onClick={() => setNewBookingTime(timeSlot)}
                          className={`
                            p-3 text-sm rounded-lg border transition-colors text-left
                            ${newBookingTime === timeSlot ?
                              'bg-blue-600 text-white border-blue-600' :
                              'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'}
                          `}
                        >
                          {timeSlot}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Registration Form */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Customer Information</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Customer Name *</label>
                    <input
                      type="text"
                      value={customerName}
                      onChange={(e) => setCustomerName(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter customer name"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Phone Number *</label>
                    <input
                      type="tel"
                      value={customerPhone}
                      onChange={(e) => setCustomerPhone(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter phone number"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                  <input
                    type="email"
                    value={customerEmail}
                    onChange={(e) => setCustomerEmail(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter email address"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Event Type *</label>
                    <select
                      value={eventType}
                      onChange={(e) => setEventType(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="">Select event type</option>
                      {eventCategories.slice(1).map((category) => (
                        <option key={category.value} value={category.value}>
                          {category.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Expected Guests *</label>
                    <input
                      type="number"
                      value={guestCount}
                      onChange={(e) => setGuestCount(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Number of guests"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Preferred Hall *</label>
                  <select
                    value={selectedHall}
                    onChange={(e) => setSelectedHall(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Select hall</option>
                    <option value="Grand Hall A">Grand Hall A (400 capacity)</option>
                    <option value="Royal Hall B">Royal Hall B (250 capacity)</option>
                    <option value="Premium Hall C">Premium Hall C (300 capacity)</option>
                    <option value="Deluxe Hall D">Deluxe Hall D (150 capacity)</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Special Requests</label>
                  <textarea
                    value={specialRequests}
                    onChange={(e) => setSpecialRequests(e.target.value)}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Any special requirements or requests..."
                  />
                </div>
              </div>

              {/* Modal Actions */}
              <div className="flex gap-3 mt-6 pt-6 border-t">
                <button
                  onClick={() => setShowBookingModal(false)}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={() => {
                    // Handle booking submission here
                    alert('Booking submitted successfully!')
                    setShowBookingModal(false)
                  }}
                  className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Submit Booking
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* All Recent Bookings Modal */}
      {showAllBookingsModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900">All Recent Bookings</h2>
                <button
                  onClick={() => setShowAllBookingsModal(false)}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {/* Search and Filter in Modal */}
              <div className="mb-6 flex gap-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Search all bookings..."
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <select
                  value={selectedEventType}
                  onChange={(e) => setSelectedEventType(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  {eventCategories.map((category) => (
                    <option key={category.value} value={category.value}>
                      {category.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Bookings List */}
              <div className="space-y-4">
                {filterBookings(bookings).map((booking) => (
                  <div key={booking.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="font-semibold text-gray-900">{booking.customerName}</h3>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(booking.status)}`}>
                            {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
                          </span>
                        </div>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600">
                          <div className="flex items-center gap-1">
                            <Calendar className="h-4 w-4" />
                            <span>{booking.eventType}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className="h-4 w-4" />
                            <span>{booking.date}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <MapPin className="h-4 w-4" />
                            <span>{booking.hall}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Users className="h-4 w-4" />
                            <span>{booking.guests} guests</span>
                          </div>
                        </div>
                        <div className="mt-2 text-sm text-gray-600">
                          <span className="font-medium">Time:</span> {booking.time}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-semibold text-gray-900">₹{booking.amount.toLocaleString()}</div>
                        <div className="text-sm text-gray-500">Total Amount</div>
                      </div>
                    </div>
                  </div>
                ))}

                {filterBookings(bookings).length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <Calendar className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                    <p>No bookings found matching your criteria.</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
