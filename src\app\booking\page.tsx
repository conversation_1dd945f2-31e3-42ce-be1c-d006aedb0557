"use client"

import { Calendar, CreditCard, DollarSign, Bell, CalendarDays, Plus, Search, Filter, Clock, Users, MapPin } from "lucide-react"
import { useState } from "react"

export default function BookingPage() {
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0])
  const [showBookingModal, setShowBookingModal] = useState(false)
  const [newBookingDate, setNewBookingDate] = useState('')
  const [newBookingTime, setNewBookingTime] = useState('')

  // Registration form states
  const [customerName, setCustomerName] = useState('')
  const [customerPhone, setCustomerPhone] = useState('')
  const [customerEmail, setCustomerEmail] = useState('')
  const [eventType, setEventType] = useState('')
  const [guestCount, setGuestCount] = useState('')
  const [selectedHall, setSelectedHall] = useState('')
  const [specialRequests, setSpecialRequests] = useState('')
  const [showFilterDropdown, setShowFilterDropdown] = useState(false)
  const [viewMode, setViewMode] = useState('selected-date') // 'selected-date' or 'all-bookings'
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedEventType, setSelectedEventType] = useState('all')
  const [showCalendar, setShowCalendar] = useState(false)

  // Event categories
  const eventCategories = [
    { value: 'all', label: 'All Events' },
    { value: 'Wedding', label: 'Wedding' },
    { value: 'Birthday', label: 'Birthday' },
    { value: 'Milad', label: 'Milad' },
    { value: 'Aqiqa', label: 'Aqiqa' },
    { value: 'Valima', label: 'Valima' },
    { value: 'Engagement', label: 'Engagement' },
    { value: 'Anniversary', label: 'Anniversary' },
    { value: 'Other', label: 'Other' }
  ]

  // Sample booking data with more dates
  const bookings = [
    {
      id: 1,
      customerName: "Ahmed Ali",
      eventType: "Wedding",
      date: "2024-01-15",
      time: "6:00 PM - 11:00 PM",
      hall: "Grand Hall A",
      guests: 300,
      status: "confirmed",
      amount: 150000
    },
    {
      id: 2,
      customerName: "Fatima Khan",
      eventType: "Engagement",
      date: "2024-01-16",
      time: "4:00 PM - 8:00 PM",
      hall: "Royal Hall B",
      guests: 150,
      status: "pending",
      amount: 80000
    },
    {
      id: 3,
      customerName: "Hassan Sheikh",
      eventType: "Reception",
      date: "2024-01-17",
      time: "7:00 PM - 12:00 AM",
      hall: "Premium Hall C",
      guests: 250,
      status: "confirmed",
      amount: 120000
    },
    {
      id: 4,
      customerName: "Zara Ahmed",
      eventType: "Wedding",
      date: "2024-01-20",
      time: "5:00 PM - 10:00 PM",
      hall: "Grand Hall A",
      guests: 400,
      status: "confirmed",
      amount: 200000
    },
    {
      id: 5,
      customerName: "Omar Khan",
      eventType: "Birthday Party",
      date: "2024-01-22",
      time: "3:00 PM - 7:00 PM",
      hall: "Royal Hall B",
      guests: 100,
      status: "pending",
      amount: 50000
    }
  ]

  // Function to get bookings for selected date
  const getBookingsForDate = (date: string) => {
    return bookings.filter(booking => booking.date === date)
  }

  // Get bookings for currently selected date
  const selectedDateBookings = getBookingsForDate(selectedDate)

  // Filter bookings based on search query and event type
  const filterBookings = (bookingsList: typeof bookings) => {
    let filtered = bookingsList

    // Filter by event type
    if (selectedEventType !== 'all') {
      filtered = filtered.filter(booking => booking.eventType === selectedEventType)
    }

    // Filter by search query
    if (searchQuery.trim()) {
      filtered = filtered.filter(booking =>
        booking.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        booking.eventType.toLowerCase().includes(searchQuery.toLowerCase()) ||
        booking.hall.toLowerCase().includes(searchQuery.toLowerCase()) ||
        booking.status.toLowerCase().includes(searchQuery.toLowerCase()) ||
        booking.date.toLowerCase().includes(searchQuery.toLowerCase()) ||
        booking.time.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    return filtered
  }

  // Get filtered bookings based on current view mode and search
  const getDisplayBookings = () => {
    if (viewMode === 'selected-date') {
      return filterBookings(selectedDateBookings)
    } else {
      return filterBookings(bookings)
    }
  }

  const displayBookings = getDisplayBookings()

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'bg-green-100 text-green-800'
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'cancelled': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="flex h-screen">
      {/* Sidebar */}
      <aside className="w-64 bg-white border-r border-gray-200 p-5">
        <h2 className="text-xl font-bold mb-6 text-gray-800">Marriage Hall</h2>
        <nav className="space-y-2">
          <a href="/" className="block px-3 py-2 rounded-md hover:bg-gray-100 text-gray-700">
            Dashboard
          </a>
          <a href="/booking" className="block px-3 py-2 rounded-md bg-blue-100 text-blue-700">
            Booking Management
          </a>
          <a href="/dashboard" className="block px-3 py-2 rounded-md hover:bg-gray-100 text-gray-700">
            Dashboard Overview
          </a>
          <a href="/customers" className="block px-3 py-2 rounded-md hover:bg-gray-100 text-gray-700">
            Customers
          </a>
          <a href="/halls" className="block px-3 py-2 rounded-md hover:bg-gray-100 text-gray-700">
            Halls
          </a>
          <a href="/settings" className="block px-3 py-2 rounded-md hover:bg-gray-100 text-gray-700">
            Settings
          </a>
        </nav>
      </aside>

      {/* Main Content */}
      <div className="flex-1 overflow-hidden bg-gray-50">
        {/* Header */}
        <header className="flex h-16 items-end justify-between border-2 ml-3 mr-2 rounded-[12px] bg-white px-4 pb-4">
          <div className="flex items-center gap-3">
            <h1 className="text-2xl text-black font-semibold">Booking Management</h1>
            <CalendarDays className="h-6 w-6 text-blue-600" />
          </div>
          <button className="rounded-full bg-gray-100 p-2 hover:bg-gray-200 transition-colors">
            <Bell className="h-5 w-5 text-gray-600" />
          </button>
        </header>

        {/* Main Content */}
        <main className="p-4 h-full overflow-auto">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 gap-4 md:grid-cols-3 mb-6">
            <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between">
                <h2 className="font-medium text-gray-900">Upcomming Booking</h2>
                <Calendar className="h-5 w-5 text-gray-400" />
              </div>
              <p className="mt-2 text-2xl font-bold text-gray-900">7</p>
              <p className="text-sm text-gray-500">+1 from yesterday</p>
            </div>

            <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between">
                <h2 className="font-medium text-gray-900">Pending Confirmations</h2>
                <CreditCard className="h-5 w-5 text-gray-400" />
              </div>
              <p className="mt-2 text-2xl font-bold text-gray-900">3</p>
              <p className="text-sm text-gray-500">Awaiting customer response</p>
            </div>

            <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between">
                <h2 className="font-medium text-gray-900">Total Revenue</h2>
                <DollarSign className="h-5 w-5 text-gray-400" />
              </div>
              <p className="mt-2 text-2xl font-bold text-gray-900">₹5,48,000</p>
              <p className="text-sm text-gray-500">+12% this month</p>
            </div>
          </div>

          {/* Action Bar */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1 flex gap-2">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search bookings..."
                  className="w-full pl-10 pr-10 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-black placeholder-gray-500"
                />
                {searchQuery && (
                  <button
                    onClick={() => setSearchQuery('')}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                    title="Clear search"
                  >
                    <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                )}
              </div>
              <div className="relative">
                <button
                  onClick={() => setShowFilterDropdown(!showFilterDropdown)}
                  className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors bg-white flex items-center gap-2"
                >
                  <Filter className="h-4 w-4 text-gray-600" />
                  <span className="text-sm text-gray-700">Filter</span>
                </button>

                {/* Filter Dropdown */}
                {showFilterDropdown && (
                  <div className="absolute top-full left-0 mt-2 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
                    <div className="p-2">
                      <button
                        onClick={() => {
                          setViewMode('selected-date')
                          setShowFilterDropdown(false)
                        }}
                        className={`w-full text-left px-3 py-2 rounded-md hover:bg-gray-50 transition-colors ${
                          viewMode === 'selected-date' ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
                        }`}
                      >
                        Selected Date Bookings
                      </button>
                      <button
                        onClick={() => {
                          setViewMode('all-bookings')
                          setShowFilterDropdown(false)
                        }}
                        className={`w-full text-left px-3 py-2 rounded-md hover:bg-gray-50 transition-colors ${
                          viewMode === 'all-bookings' ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
                        }`}
                      >
                        All Recent Bookings
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
            <div className="flex gap-2">
              {/* Event Categories Dropdown */}
              <div className="relative">
                <select
                  value={selectedEventType}
                  onChange={(e) => setSelectedEventType(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-black appearance-none pr-8 min-w-[140px]"
                >
                  {eventCategories.map((category) => (
                    <option key={category.value} value={category.value} className="text-black">
                      {category.label}
                    </option>
                  ))}
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                  <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </div>

              <button
                onClick={() => setShowBookingModal(true)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center gap-2 transition-colors shadow-sm"
              >
                <Plus className="h-4 w-4" />
                New Booking
              </button>
            </div>
          </div>

          {/* Calendar Date Status / Search Results */}
          <div className="mb-6">
            <div className="rounded-lg border border-gray-200 bg-white shadow-sm p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <h3 className="text-lg font-semibold text-gray-900">
                    {searchQuery ? `Search Results for "${searchQuery}"` : 'Date Status & Calendar'}
                  </h3>
                  <Calendar className="h-5 w-5 text-blue-600" />
                </div>

                {!searchQuery && (
                  <button
                    onClick={() => setShowCalendar(!showCalendar)}
                    className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
                  >
                    <Calendar className="h-4 w-4" />
                    {showCalendar ? 'Hide Calendar' : 'Show Calendar'}
                  </button>
                )}
              </div>

              {!searchQuery && showCalendar && (
                /* Interactive Calendar */
                <div className="mb-6">
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="text-md font-medium text-gray-900 mb-3">Select Date from Calendar</h4>

                    {/* Calendar Grid */}
                    <div className="grid grid-cols-7 gap-1 mb-4">
                      {/* Calendar Header */}
                      {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
                        <div key={day} className="text-center text-sm font-medium text-gray-600 py-2">
                          {day}
                        </div>
                      ))}

                      {/* Calendar Days */}
                      {(() => {
                        const today = new Date()
                        const currentMonth = today.getMonth()
                        const currentYear = today.getFullYear()
                        const firstDay = new Date(currentYear, currentMonth, 1)
                        const lastDay = new Date(currentYear, currentMonth + 1, 0)
                        const startDate = new Date(firstDay)
                        startDate.setDate(startDate.getDate() - firstDay.getDay())

                        const days = []
                        for (let i = 0; i < 42; i++) {
                          const date = new Date(startDate.getTime() + (i * 24 * 60 * 60 * 1000))
                          const dateStr = date.toISOString().split('T')[0]
                          const isCurrentMonth = date.getMonth() === currentMonth
                          const isToday = dateStr === today.toISOString().split('T')[0]
                          const isSelected = dateStr === selectedDate
                          const dayBookings = getBookingsForDate(dateStr)
                          const isPast = date < today && !isToday

                          days.push(
                            <button
                              key={dateStr}
                              onClick={() => !isPast && setSelectedDate(dateStr)}
                              disabled={isPast}
                              className={`
                                relative p-2 text-sm rounded-lg transition-all duration-200 min-h-[40px] flex flex-col items-center justify-center
                                ${isPast
                                  ? 'text-gray-300 cursor-not-allowed'
                                  : isSelected
                                    ? 'bg-blue-600 text-white shadow-lg'
                                    : isCurrentMonth
                                      ? dayBookings.length > 0
                                        ? 'bg-red-100 text-red-700 hover:bg-red-200'
                                        : 'bg-green-100 text-green-700 hover:bg-green-200'
                                      : 'text-gray-400 hover:bg-gray-100'
                                }
                                ${isToday && !isSelected ? 'ring-2 ring-blue-400' : ''}
                              `}
                            >
                              <span className="font-medium">{date.getDate()}</span>
                              {isCurrentMonth && dayBookings.length > 0 && (
                                <div className="absolute bottom-1 flex gap-0.5">
                                  {dayBookings.slice(0, 3).map((_, idx) => (
                                    <div key={idx} className="w-1 h-1 bg-current rounded-full opacity-70"></div>
                                  ))}
                                </div>
                              )}
                            </button>
                          )
                        }
                        return days
                      })()}
                    </div>

                    {/* Calendar Legend */}
                    <div className="flex flex-wrap gap-4 text-xs">
                      <div className="flex items-center gap-1">
                        <div className="w-3 h-3 bg-green-100 rounded"></div>
                        <span className="text-gray-600">Available</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <div className="w-3 h-3 bg-red-100 rounded"></div>
                        <span className="text-gray-600">Booked</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <div className="w-3 h-3 bg-blue-600 rounded"></div>
                        <span className="text-gray-600">Selected</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <div className="w-3 h-3 border-2 border-blue-400 rounded"></div>
                        <span className="text-gray-600">Today</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {searchQuery ? (
                // Search Results Display
                <div className="space-y-3">
                  {displayBookings.length > 0 ? (
                    <div>
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-3">
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                          <span className="text-sm font-medium text-blue-700">
                            Found {displayBookings.length} booking(s)
                            {searchQuery && ` matching "${searchQuery}"`}
                            {selectedEventType !== 'all' && ` for ${selectedEventType} events`}
                          </span>
                        </div>
                      </div>

                      <div className="space-y-2">
                        {displayBookings.map((booking) => (
                          <div key={booking.id} className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <div className="flex justify-between items-start">
                              <div>
                                <p className="font-semibold text-gray-900">{booking.customerName}</p>
                                <p className="text-sm text-gray-600">{booking.eventType} at {booking.hall}</p>
                                <p className="text-sm font-medium text-blue-700">
                                  {booking.date} at {booking.time}
                                </p>
                                <p className="text-sm text-gray-600">{booking.guests} guests • ₹{booking.amount.toLocaleString()}</p>
                              </div>
                              <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(booking.status)}`}>
                                {booking.status}
                              </span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-6">
                      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                        <div className="text-gray-500 mb-2">No bookings found for "{searchQuery}"</div>
                        <button
                          onClick={() => setSearchQuery('')}
                          className="text-blue-600 hover:text-blue-800 text-sm underline"
                        >
                          
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                // Date Status Display
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-sm font-medium text-gray-700">Selected Date:</span>
                    <span className="text-sm font-semibold text-gray-900">
                      {new Date(selectedDate).toLocaleDateString('en-US', {
                        weekday: 'long',
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}
                    </span>
                  </div>

                  <div>
                    <div className="flex items-center gap-2 mb-4">
                      <span className="text-sm font-medium text-gray-700">Selected Date:</span>
                      <span className="text-sm font-semibold text-blue-700">
                        {new Date(selectedDate).toLocaleDateString('en-US', {
                          weekday: 'long',
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })}
                      </span>
                    </div>

                    {/* Time Slots Status */}
                    <div className="space-y-3">
                      <h4 className="text-sm font-medium text-gray-900">Available Time Slots:</h4>

                      {/* All possible time slots */}
                      {[
                        '10:00 AM - 2:00 PM',
                        '2:00 PM - 6:00 PM',
                        '6:00 PM - 10:00 PM',
                        '10:00 PM - 2:00 AM',
                        'Full Day'
                      ].map((timeSlot) => {
                        const bookedSlot = selectedDateBookings.find(booking => booking.time === timeSlot)
                        const isAvailable = !bookedSlot

                        return (
                          <div key={timeSlot} className={`
                            p-3 rounded-lg border-2 transition-all duration-200
                            ${isAvailable
                              ? 'border-green-200 bg-green-50 hover:bg-green-100'
                              : 'border-red-200 bg-red-50'
                            }
                          `}>
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <div className={`w-3 h-3 rounded-full ${
                                  isAvailable ? 'bg-green-600' : 'bg-red-600'
                                }`}></div>
                                <span className="font-medium text-gray-900">{timeSlot}</span>
                                <span className={`text-xs px-2 py-1 rounded-full font-medium ${
                                  isAvailable
                                    ? 'bg-green-100 text-green-700'
                                    : 'bg-red-100 text-red-700'
                                }`}>
                                  {isAvailable ? 'Available' : 'Booked'}
                                </span>
                              </div>

                              <div className="flex items-center gap-2">
                                {bookedSlot ? (
                                  <div className="text-right">
                                    <p className="text-sm font-medium text-gray-900">{bookedSlot.customerName}</p>
                                    <p className="text-xs text-gray-600">{bookedSlot.eventType}</p>
                                  </div>
                                ) : (
                                  <button
                                    onClick={() => {
                                      setNewBookingDate(selectedDate)
                                      setNewBookingTime(timeSlot)
                                      setShowBookingModal(true)
                                    }}
                                    className="px-3 py-1 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors text-xs flex items-center gap-1"
                                  >
                                    <Plus className="h-3 w-3" />
                                    Book Now
                                  </button>
                                )}
                              </div>
                            </div>
                          </div>
                        )
                      })}
                    </div>

                    {/* Summary */}
                    <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-blue-600" />
                          <span className="text-sm font-medium text-blue-700">
                            {selectedDateBookings.length === 0
                              ? 'All time slots available'
                              : `${5 - selectedDateBookings.length} of 5 time slots available`
                            }
                          </span>
                        </div>
                        {selectedDateBookings.length === 0 && (
                          <button
                            onClick={() => {
                              setNewBookingDate(selectedDate)
                              setShowBookingModal(true)
                            }}
                            className="px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-xs flex items-center gap-1"
                          >
                            <Plus className="h-3 w-3" />
                            Quick Book
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>



        </main>
      </div>

      {/* New Booking Modal */}
      {showBookingModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              {/* Modal Header */}
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-3">
                  <h2 className="text-2xl font-semibold text-gray-900">New Booking</h2>
                  <CalendarDays className="h-6 w-6 text-green-600" />
                </div>
                <button
                  onClick={() => setShowBookingModal(false)}
                  className="text-gray-800 hover:text-gray-600 transition-colors"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {/* Calendar Section */}
              <div className="mb-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Select Date & Time</h3>

                {/* Date Selection */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-800 mb-2">
                      Select Date
                    </label>
                    <input
                      type="date"
                      value={newBookingDate}
                      onChange={(e) => setNewBookingDate(e.target.value)}
                      min={new Date().toISOString().split('T')[0]}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-black"
                    />

                    {/* Date Status */}
                    {newBookingDate && (
                      <div className="mt-3 p-3 rounded-lg bg-gray-80">
                        <div className="flex items-center gap-2 mb-2">
                          <span className="text-sm font-medium text-gray-900">Selected:</span>
                          <span className="text-sm font-semibold text-gray-900">
                            {new Date(newBookingDate).toLocaleDateString('en-US', {
                              weekday: 'long',
                              year: 'numeric',
                              month: 'long',
                              day: 'numeric'
                            })}
                          </span>
                        </div>

                        {getBookingsForDate(newBookingDate).length > 0 ? (
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                            <span className="text-sm text-yellow-700">
                              {getBookingsForDate(newBookingDate).length} booking(s) already exist
                            </span>
                          </div>
                        ) : (
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                            <span className="text-sm text-green-700">Date is available</span>
                          </div>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Time Selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Select Time Slot
                    </label>
                    <select
                      value={newBookingTime}
                      onChange={(e) => setNewBookingTime(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-black"
                    >
                      <option value="" className="text-black">Choose time slot</option>
                      <option value="10:00 AM - 2:00 PM" className="text-black">10:00 AM - 2:00 PM (Morning)</option>
                      <option value="2:00 PM - 6:00 PM" className="text-black">2:00 PM - 6:00 PM (Afternoon)</option>
                      <option value="6:00 PM - 10:00 PM" className="text-black">6:00 PM - 10:00 PM (Evening)</option>
                      <option value="10:00 PM - 2:00 AM" className="text-black">10:00 PM - 2:00 AM (Night)</option>
                      <option value="Full Day" className="text-black">Full Day (10:00 AM - 2:00 AM)</option>
                    </select>

                    {newBookingTime && (
                      <div className="mt-3 p-3 rounded-lg bg-blue-50">
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4 text-blue-600" />
                          <span className="text-sm font-medium text-blue-700">
                            Selected: {newBookingTime}
                          </span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Registration Form */}
              <div className="mb-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Customer Information</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Customer Name */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Customer Name *
                    </label>
                    <input
                      type="text"
                      value={customerName}
                      onChange={(e) => setCustomerName(e.target.value)}
                      placeholder="Enter full name"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-black"
                      required
                    />
                  </div>

                  {/* Phone Number */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Phone Number *
                    </label>
                    <input
                      type="tel"
                      value={customerPhone}
                      onChange={(e) => setCustomerPhone(e.target.value)}
                      placeholder="Enter phone number"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-black"
                      required
                    />
                  </div>

                  {/* Email */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Email Address
                    </label>
                    <input
                      type="email"
                      value={customerEmail}
                      onChange={(e) => setCustomerEmail(e.target.value)}
                      placeholder="Enter email address"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-black"
                    />
                  </div>

                  {/* Event Type */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Event Type *
                    </label>
                    <select
                      value={eventType}
                      onChange={(e) => setEventType(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-black"
                      required
                    >
                      <option value="" className="text-black">Select event type</option>
                      <option value="Wedding" className="text-black">Wedding</option>
                      <option value="Birthday" className="text-black">Birthday</option>
                      <option value="Milad" className="text-black">Milad</option>
                      <option value="Aqiqa" className="text-black">Aqiqa</option>
                      <option value="Valima" className="text-black">Valima</option>
                      <option value="Engagement" className="text-black">Engagement</option>
                      <option value="Anniversary" className="text-black">Anniversary</option>
                      <option value="Other" className="text-black">Other</option>
                    </select>
                  </div>

                  {/* Guest Count */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Number of Guests *
                    </label>
                    <input
                      type="number"
                      value={guestCount}
                      onChange={(e) => setGuestCount(e.target.value)}
                      placeholder="Enter guest count"
                      min="1"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-black"
                      required
                    />
                  </div>

                  {/* Hall Selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Select Hall *
                    </label>
                    <select
                      value={selectedHall}
                      onChange={(e) => setSelectedHall(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-black"
                      required
                    >
                      <option value="" className="text-black">Select hall</option>
                      <option value="Grand Hall A" className="text-black">Grand Hall A (500 capacity)</option>
                      <option value="Grand Hall B" className="text-black">Grand Hall B (300 capacity)</option>
                      <option value="Royal Hall" className="text-black">Royal Hall (200 capacity)</option>
                      <option value="Garden Hall" className="text-black">Garden Hall (150 capacity)</option>
                    </select>
                  </div>
                </div>

                {/* Special Requests */}
                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Special Requests / Additional Notes
                  </label>
                  <textarea
                    value={specialRequests}
                    onChange={(e) => setSpecialRequests(e.target.value)}
                    placeholder="Any special requirements, decorations, catering preferences, etc."
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-black resize-none"
                  />
                </div>
              </div>

              {/* Existing Bookings for Selected Date */}
              {newBookingDate && getBookingsForDate(newBookingDate).length > 0 && (
                <div className="mb-6">
                  <h4 className="text-md font-medium text-gray-900 mb-3">Existing Bookings for This Date</h4>
                  <div className="space-y-2">
                    {getBookingsForDate(newBookingDate).map((booking) => (
                      <div key={booking.id} className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                        <div className="flex justify-between items-center">
                          <div>
                            <p className="font-medium text-gray-900">{booking.customerName}</p>
                            <p className="text-sm text-gray-600">{booking.eventType} - {booking.hall}</p>
                          </div>
                          <div className="text-right">
                            <p className="text-sm font-medium text-gray-900">{booking.time}</p>
                            <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(booking.status)}`}>
                              {booking.status}
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex gap-3 justify-end">
                <button
                  onClick={() => setShowBookingModal(false)}
                  className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors text-gray-700"
                >
                  Cancel
                </button>
                <button
                  onClick={() => {
                    // Validation
                    if (!newBookingDate || !newBookingTime) {
                      alert('Please select both date and time')
                      return
                    }
                    if (!customerName || !customerPhone || !eventType || !guestCount || !selectedHall) {
                      alert('Please fill in all required fields (marked with *)')
                      return
                    }

                    // Success message with all details
                    const bookingDetails = `
Booking Confirmed Successfully!

Customer: ${customerName}
Phone: ${customerPhone}
${customerEmail ? `Email: ${customerEmail}` : ''}
Event: ${eventType}
Date: ${newBookingDate}
Time: ${newBookingTime}
Guests: ${guestCount}
Hall: ${selectedHall}
${specialRequests ? `Special Requests: ${specialRequests}` : ''}
                    `

                    alert(bookingDetails)

                    // Reset form
                    setShowBookingModal(false)
                    setNewBookingDate('')
                    setNewBookingTime('')
                    setCustomerName('')
                    setCustomerPhone('')
                    setCustomerEmail('')
                    setEventType('')
                    setGuestCount('')
                    setSelectedHall('')
                    setSpecialRequests('')
                  }}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Confirm Booking
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}