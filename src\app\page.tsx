'use client';

import { ReactNode } from "react";

export default function DashboardLayout({ children }: { children: ReactNode }) {
  return (
    <div className="flex h-screen">
      {/* Sidebar */}
      <aside className="w-64 bg-gray-100 p-5">
        <h2 className="text-xl font-bold mb-6">Booking System</h2>
        <ul className="space-y-4">
          <li><a href="/dashboard" className="block hover:text-blue-600">Dashboard</a></li>
          <li><a href="/booking" className="block hover:text-blue-600">Booking</a></li>
          <li><a href="/customer" className="block hover:text-blue-600">Customer</a></li>
          <li><a href="/settings" className="block hover:text-blue-600">Settings</a></li>
        </ul>
      </aside>

      {/* Main Content */}
      <main className="flex-1 bg-gray-50 p-6 overflow-y-auto">
        {children}
      </main>
    </div>
  );
}
