"use client"

import { useState, use<PERSON>emo, useRef, useEffect } from "react"
import { Bell, Calendar, CreditCard, DollarSign, Filter, ChevronDown, Check } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Button } from "@/components/ui/button"
import { notificationsData, bookingsData } from "@/constant/data"

// Custom Checkbox Component
const CustomCheckbox = ({
  checked,
  onCheckedChange,
  id,
  className = "",
}: {
  checked: boolean
  onCheckedChange: (checked: boolean) => void
  id: string
  className?: string
}) => {
  return (
    <div className="relative">
      <input
        type="checkbox"
        id={id}
        checked={checked}
        onChange={(e) => onCheckedChange(e.target.checked)}
        className="sr-only"
      />
      <div
        className={`h-4 w-4 rounded border-2 border-gray-300 cursor-pointer transition-all ${
          checked ? "bg-black border-black" : "bg-white hover:border-gray-400"
        } ${className}`}
        onClick={() => onCheckedChange(!checked)}
      >
        {checked && <Check className="h-3 w-3 text-white absolute top-0.5 left-0.5" />}
      </div>
    </div>
  )
}

// Helper function to get border color
const getBorderColor = (borderColor: string) => {
  switch (borderColor) {
    case "blue-500":
      return "#3B82F6"
    case "green-500":
      return "#10B981"
    case "purple-500":
      return "#8B5CF6"
    case "orange-500":
      return "#F97316"
    case "red-500":
      return "#EF4444"
    case "yellow-500":
      return "#EAB308"
    case "teal-500":
      return "#14B8A6"
    case "pink-500":
      return "#EC4899"
    case "indigo-500":
      return "#6366F1"
    case "cyan-500":
      return "#06B6D4"
    default:
      return "#6B7280"
  }
}

// Helper function to get background color
const getBgColor = (bgColor: string) => {
  switch (bgColor) {
    case "blue-100":
      return "#DBEAFE"
    case "green-100":
      return "#D1FAE5"
    case "purple-100":
      return "#EDE9FE"
    case "orange-100":
      return "#FED7AA"
    case "red-100":
      return "#FEE2E2"
    case "yellow-100":
      return "#FEF3C7"
    case "teal-100":
      return "#CCFBF1"
    case "pink-100":
      return "#FCE7F3"
    case "indigo-100":
      return "#E0E7FF"
    case "cyan-100":
      return "#CFFAFE"
    default:
      return "#F3F4F6"
  }
}

// Helper function to get text color
const getTextColor = (textColor: string) => {
  switch (textColor) {
    case "blue-500":
      return "#3B82F6"
    case "green-500":
      return "#10B981"
    case "purple-500":
      return "#8B5CF6"
    case "orange-500":
      return "#F97316"
    case "red-500":
      return "#EF4444"
    case "yellow-500":
      return "#EAB308"
    case "teal-500":
      return "#14B8A6"
    case "pink-500":
      return "#EC4899"
    case "indigo-500":
      return "#6366F1"
    case "cyan-500":
      return "#06B6D4"
    default:
      return "#6B7280"
  }
}

export default function DashboardPage() {
  // Filter states - empty arrays mean show all data
  const [selectedCategories, setSelectedCategories] = useState<string[]>([])
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([])
  const [isFilterOpen, setIsFilterOpen] = useState(false)
  const filterRef = useRef<HTMLDivElement>(null)
  const buttonRef = useRef<HTMLButtonElement>(null)

  // Close filter when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        filterRef.current &&
        buttonRef.current &&
        !filterRef.current.contains(event.target as Node) &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsFilterOpen(false)
      }
    }

    if (isFilterOpen) {
      document.addEventListener("mousedown", handleClickOutside)
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [isFilterOpen])

  // Category counts for notifications
  const categoryCounts = useMemo(() => {
    const counts: Record<string, number> = {}
    notificationsData.forEach((item) => {
      counts[item.category] = (counts[item.category] || 0) + 1
    })
    return counts
  }, [])

  // Status counts for notifications
  const statusCounts = useMemo(() => {
    const counts: Record<string, number> = {}
    notificationsData.forEach((item) => {
      counts[item.status] = (counts[item.status] || 0) + 1
    })
    return counts
  }, [])

  // Filtered data - if no filters selected, show all data
  const filteredNotifications = useMemo(() => {
    if (selectedCategories.length === 0 && selectedStatuses.length === 0) {
      return notificationsData // Show all data by default
    }

    return notificationsData.filter((item) => {
      const categoryMatch = selectedCategories.length === 0 || selectedCategories.includes(item.category)
      const statusMatch = selectedStatuses.length === 0 || selectedStatuses.includes(item.status)
      return categoryMatch && statusMatch
    })
  }, [selectedCategories, selectedStatuses])

  const filteredBookings = useMemo(() => {
    if (selectedCategories.length === 0) {
      return bookingsData // Show all data by default
    }
    return bookingsData.filter((item) => selectedCategories.includes(item.category))
  }, [selectedCategories])

  // Handle category filter change
  const handleCategoryChange = (category: string, checked: boolean) => {
    if (checked) {
      setSelectedCategories([...selectedCategories, category])
    } else {
      setSelectedCategories(selectedCategories.filter((c) => c !== category))
    }
  }

  // Handle status filter change
  const handleStatusChange = (status: string, checked: boolean) => {
    if (checked) {
      setSelectedStatuses([...selectedStatuses, status])
    } else {
      setSelectedStatuses(selectedStatuses.filter((s) => s !== status))
    }
  }

  return (
    <div className="flex-1 overflow-hidden">
      <header className="flex h-16 items-end justify-between border-2 ml-3 mr-2 rounded-[12px] bg-white px-4 pb-4">
        <h1 className="text-2xl font-semibold">Dashboard</h1>
        <button className="rounded-full bg-gray-100 p-2">
          <Bell className="h-5 w-5" />
        </button>
      </header>

      <main className="p-4 h-full overflow-auto">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 gap-4 md:grid-cols-3 mb-6">
          <div className="rounded-lg border bg-white p-4 shadow-sm">
            <div className="flex items-center justify-between">
              <h2 className="font-medium">Up coming bookings</h2>
              <Calendar className="h-5 w-5 text-gray-400" />
            </div>
            <p className="mt-2 text-2xl font-bold">12</p>
            <p className="text-sm text-gray-500">+2 from yesterday</p>
          </div>

          <div className="rounded-lg border bg-white p-4 shadow-sm">
            <div className="flex items-center justify-between">
              <h2 className="font-medium">Pending Payments</h2>
              <CreditCard className="h-5 w-5 text-gray-400" />
            </div>
            <p className="mt-2 text-2xl font-bold">$ 2,350</p>
            <p className="text-sm text-gray-500">3 payments awaiting</p>
          </div>

          <div className="rounded-lg border bg-white p-4 shadow-sm">
            <div className="flex items-center justify-between">
              <h2 className="font-medium">Total Revenue</h2>
              <DollarSign className="h-5 w-5 text-gray-400" />
            </div>
            <p className="mt-2 text-2xl font-bold">$ 12,234</p>
            <p className="text-sm text-gray-500">+8% from last month</p>
          </div>
        </div>

        {/* Enhanced Notifications and Bookings Section */}
        <div className="space-y-4 h-full">
          <Tabs defaultValue="notifications">
            <TabsList className="grid grid-cols-2 w-[400px] bg-gray-200">
              <TabsTrigger value="notifications" className="relative">
                Notifications
                <span className="ml-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  {filteredNotifications.length}
                </span>
              </TabsTrigger>
              <TabsTrigger value="bookings" className="relative">
                Today's Bookings
                <span className="ml-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  {filteredBookings.length}
                </span>
              </TabsTrigger>
            </TabsList>

            <Card className="h-full">
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle className="text-xl">Recent Activity</CardTitle>

                {/* Custom Filter Dropdown */}
                <div className="relative">
                  <Button
                    ref={buttonRef}
                    variant="outline"
                    className="flex items-center gap-2 hover:bg-gray-50 transition-colors"
                    onClick={() => setIsFilterOpen(!isFilterOpen)}
                  >
                    <Filter className="h-4 w-4" />
                    Filter
                    <span className="bg-gray-200 text-gray-700 text-xs rounded-full h-5 w-5 flex items-center justify-center">
                      6
                    </span>
                    <ChevronDown className={`h-4 w-4 transition-transform ${isFilterOpen ? "rotate-180" : ""}`} />
                  </Button>

                  {/* Filter Dropdown Content */}
                  {isFilterOpen && (
                    <div
                      ref={filterRef}
                      className="absolute right-0 top-full mt-2 w-72 bg-white rounded-xl border border-gray-200 shadow-xl z-50 max-h-[400px] overflow-y-auto"
                    >
                      <div className="px-4 py-3 border-b border-gray-100 bg-gradient-to-r from-gray-50 to-gray-100 sticky top-0 z-10 rounded-t-xl">
                        <h3 className="font-semibold text-base text-gray-900">Filter Notifications</h3>
                        <p className="text-sm text-gray-600 mt-0.5">Choose which notifications to display</p>
                      </div>

                      <div className="p-4 space-y-4">
                        {/* Type Filters */}
                        <div>
                          <h4 className="font-semibold text-sm text-gray-900 mb-2 px-2 py-1 bg-gray-100 rounded-md">
                            Type
                          </h4>
                          <div className="space-y-1.5">
                            {["Bookings", "Payments", "Customers", "Reminders"].map((category) => (
                              <div
                                key={category}
                                className="flex items-center justify-between py-1.5 hover:bg-gray-50 rounded-md px-2 -mx-2 transition-colors cursor-pointer"
                                onClick={() => handleCategoryChange(category, !selectedCategories.includes(category))}
                              >
                                <div className="flex items-center space-x-2.5">
                                  <CustomCheckbox
                                    id={category}
                                    checked={selectedCategories.includes(category)}
                                    onCheckedChange={(checked) => handleCategoryChange(category, checked)}
                                  />
                                  <label
                                    htmlFor={category}
                                    className="text-sm font-medium text-gray-700 cursor-pointer"
                                  >
                                    {category}
                                  </label>
                                </div>
                                <span className="text-xs font-semibold text-gray-600 bg-gray-100 px-2 py-0.5 rounded-full min-w-[24px] text-center">
                                  {categoryCounts[category] || 0}
                                </span>
                              </div>
                            ))}
                          </div>
                        </div>

                        {/* Status Filters */}
                        <div>
                          <h4 className="font-semibold text-sm text-gray-900 mb-2 px-2 py-1 bg-gray-100 rounded-md">
                            Status
                          </h4>
                          <div className="space-y-1.5">
                            {["Unread", "Read"].map((status) => (
                              <div
                                key={status}
                                className="flex items-center justify-between py-1.5 hover:bg-gray-50 rounded-md px-2 -mx-2 transition-colors cursor-pointer"
                                onClick={() => handleStatusChange(status, !selectedStatuses.includes(status))}
                              >
                                <div className="flex items-center space-x-2.5">
                                  <CustomCheckbox
                                    id={status}
                                    checked={selectedStatuses.includes(status)}
                                    onCheckedChange={(checked) => handleStatusChange(status, checked)}
                                  />
                                  <label htmlFor={status} className="text-sm font-medium text-gray-700 cursor-pointer">
                                    {status}
                                  </label>
                                </div>
                                <span className="text-xs font-semibold text-gray-600 bg-gray-100 px-2 py-0.5 rounded-full min-w-[24px] text-center">
                                  {statusCounts[status] || 0}
                                </span>
                              </div>
                            ))}
                          </div>
                        </div>

                        {/* Clear All Button */}
                        {(selectedCategories.length > 0 || selectedStatuses.length > 0) && (
                          <div className="pt-2 border-t border-gray-200">
                            <button
                              onClick={() => {
                                setSelectedCategories([])
                                setSelectedStatuses([])
                              }}
                              className="w-full text-sm text-red-600 hover:text-red-800 font-medium px-3 py-2 rounded-md hover:bg-red-50 transition-colors border border-red-200 hover:border-red-300"
                            >
                              Clear All Filters
                            </button>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </CardHeader>

              <CardContent>
                {/* Notifications */}
                <TabsContent value="notifications" className="h-full m-0">
                  <div className="px-6 pb-2">
                    <p className="text-sm text-muted-foreground">
                      Showing {filteredNotifications.length} of {notificationsData.length} notifications
                    </p>
                  </div>
                  <ScrollArea className="h-[350px] px-6">
                    <div className="space-y-2 pb-4">
                      {filteredNotifications.map((notification, index) => (
                        <div
                          key={index}
                          className="bg-white rounded-lg overflow-hidden flex items-start gap-3 hover:bg-gray-50 transition-colors relative border border-gray-200 shadow-sm"
                          style={{
                            borderLeft: `4px solid ${getBorderColor(notification.borderColor)}`,
                            borderRadius: "8px",
                          }}
                        >
                          {/* Card Content */}
                          <div className="flex items-start gap-3 p-3 w-full">
                            {/* Icon */}
                            <div
                              className="flex h-8 w-8 items-center justify-center rounded-full flex-shrink-0"
                              style={{ backgroundColor: getBgColor(notification.iconBg) }}
                            >
                              <notification.icon
                                className="h-4 w-4"
                                style={{ color: getTextColor(notification.iconColor) }}
                              />
                            </div>

                            {/* Content */}
                            <div className="flex-1 min-w-0">
                              <h3 className="font-medium text-sm text-gray-900">{notification.type}</h3>
                              <p className="text-xs text-gray-600 mt-0.5">{notification.description}</p>
                              <p className="text-xs text-gray-500 mt-0.5">{notification.time}</p>
                            </div>

                            {/* Rounded Badge */}
                            {notification.badge && (
                              <button className="bg-black text-white text-xs px-3 py-1 rounded-full h-6 flex items-center">
                                {notification.badge}
                              </button>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </TabsContent>

                {/* Today's Bookings */}
                <TabsContent value="bookings" className="h-full m-0">
                  <div className="px-6 pb-2">
                    <p className="text-sm text-muted-foreground">
                      You have {filteredBookings.length} bookings scheduled for today
                    </p>
                  </div>
                  <ScrollArea className="h-[350px] px-6">
                    <div className="space-y-2 pb-4">
                      {filteredBookings.map((booking, index) => (
                        <div
                          key={index}
                          className="bg-white rounded-lg overflow-hidden flex items-start gap-3 hover:bg-gray-50 transition-colors relative border border-gray-200 shadow-sm"
                          style={{
                            borderLeft: `4px solid ${getBorderColor(booking.borderColor)}`,
                            borderRadius: "8px",
                          }}
                        >
                          {/* Card Content */}
                          <div className="flex items-start gap-3 p-3 w-full">
                            {/* Icon */}
                            <div
                              className="flex h-8 w-8 items-center justify-center rounded-full flex-shrink-0"
                              style={{ backgroundColor: getBgColor(booking.iconBg) }}
                            >
                              <booking.icon className="h-4 w-4" style={{ color: getTextColor(booking.iconColor) }} />
                            </div>

                            {/* Content */}
                            <div className="flex-1 min-w-0">
                              <h3 className="font-medium text-sm text-gray-900">{booking.name}</h3>
                              <p className="text-xs text-gray-600 mt-0.5">{booking.service}</p>
                              <p className="text-xs text-gray-500 mt-0.5">{booking.duration}</p>
                            </div>

                            {/* Rounded Badge */}
                            <button className="bg-black text-white text-xs px-3 py-1 rounded-full h-6 flex items-center">
                              {booking.badge}
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </TabsContent>
              </CardContent>
            </Card>
          </Tabs>
        </div>
      </main>
    </div>
  )
}
