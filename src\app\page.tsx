export default function HomePage() {
  return (
    <div className="flex h-screen">
      {/* Sidebar */}
      <aside className="w-64 bg-white border-r border-gray-200 p-5">
        <h2 className="text-xl font-bold mb-6 text-gray-800">Marriage Hall</h2>
        <nav className="space-y-2">
          <a href="/" className="block px-3 py-2 rounded-md bg-blue-100 text-blue-700">
            Dashboard
          </a>
          <a href="/booking" className="block px-3 py-2 rounded-md hover:bg-gray-100 text-gray-700">
            Booking Management
          </a>
          <a href="/dashboard" className="block px-3 py-2 rounded-md hover:bg-gray-100 text-gray-700">
            Dashboard Overview
          </a>
          <a href="/customers" className="block px-3 py-2 rounded-md hover:bg-gray-100 text-gray-700">
            Customers
          </a>
          <a href="/halls" className="block px-3 py-2 rounded-md hover:bg-gray-100 text-gray-700">
            Halls
          </a>
          <a href="/settings" className="block px-3 py-2 rounded-md hover:bg-gray-100 text-gray-700">
            Settings
          </a>
        </nav>
      </aside>

      {/* Main Content */}
      <main className="flex-1 bg-gray-50 p-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">
            Welcome to Marriage Hall Booking System
          </h1>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <h3 className="text-lg font-semibold mb-2">Quick Actions</h3>
              <div className="space-y-2">
                <a href="/booking" className="block text-blue-600 hover:text-blue-800">
                  → Manage Bookings
                </a>
                <a href="/dashboard" className="block text-blue-600 hover:text-blue-800">
                  → View Dashboard
                </a>
                <a href="/customers" className="block text-blue-600 hover:text-blue-800">
                  → Customer Management
                </a>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <h3 className="text-lg font-semibold mb-2">Today's Stats</h3>
              <div className="space-y-2">
                <p className="text-sm text-gray-600">Total Bookings: <span className="font-medium">7</span></p>
                <p className="text-sm text-gray-600">Pending: <span className="font-medium">3</span></p>
                <p className="text-sm text-gray-600">Revenue: <span className="font-medium">₹5,48,000</span></p>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <h3 className="text-lg font-semibold mb-2">Recent Activity</h3>
              <div className="space-y-2">
                <p className="text-sm text-gray-600">New booking from Ahmed Ali</p>
                <p className="text-sm text-gray-600">Payment received from Fatima</p>
                <p className="text-sm text-gray-600">Hall A confirmed for tomorrow</p>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}